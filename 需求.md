最小生成树问题

【问题描述】

若要在n个城市之间建设通信网络，只需要假设n-1条线路即可。如何以最低的经济代价建设这个通信网，是一个网的最小生成树问题。
基本要求】
1.利用克鲁斯卡尔算法求网的最小生成树。
2利用普里姆算法求网的最小生成树。
3.要求输出各条边及它们的权值。
通信线路一旦建成，必然是双向的。因此，构造最小生成树的网一定是无向网。设图的顶点数不超过 30个，并为简单起见，网中边的权值设成小于100的整数，可利用C语言提供的随机函数产生。
图的存储结构的选取应和所作操作相适应。为了便于选择权值最小的边，此题的存储结构既不选用邻接矩阵的数组表示法，也不选用邻接表，而是以存储边(带权)的数组表示图。
利用堆排序实现选择权值最小的边。

```
使用C语言实现,变量起名要简洁不要太长,各个方法的变量要定义在方法的最开始,变量名可以牺牲可读性采用部分简写,你需要为我的代码添加适量注释,要求口语一些,但不能过于夸张,代码中输入的提示内容也要偏口语但不能过度夸张,所有代码在一个文件中完成
菜单项采用
========================
     ★   菜单  ★
========================
  a.
  b.
  c.
  ...
========================
的类似样式
```

