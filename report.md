# 最小生成树问题的设计与实现 - 课程设计报告

## 1. 课程设计概述

### 1.1 问题描述

这次课程设计我做的是最小生成树问题，说白了就是要解决在n个城市之间建设通信网络的问题。其实刚开始看到这个题目的时候，我还挺懵的，什么叫最小生成树啊？后来老师解释了一下，我才明白原来这是一个很实际的问题。比如说现在有好几个城市，我们要在它们之间建设通信线路，但是建设成本是有限的，我们希望用最少的钱把所有城市都连通起来，这就是最小生成树要解决的核心问题。

从数学角度来说，这个问题就是要在一个连通的带权无向图中找到一棵生成树，使得这棵树的所有边的权值之和最小。听起来有点绕，但其实就是要找到连接所有顶点的最便宜的方案。在我的实现中，我不仅要能够求解这个问题，还要能够随机生成测试用的图，并且要用两种不同的算法来求解，这样可以对比它们的效果和效率。

### 1.2 基本要求

老师给我们的要求还挺详细的，我总结了一下主要有这么几个方面。首先是算法实现方面，要求我们必须实现克鲁斯卡尔算法和普里姆算法这两种经典的最小生成树算法，而且要能够输出每条边的信息和它们的权值。其次是数据生成方面，要求能够处理顶点数不超过30个的图，边的权值要设置成小于100的整数，并且可以利用随机函数来生成测试数据。

在存储结构的选择上，老师特别强调了要根据算法的特点来选择合适的数据结构，不能简单地用邻接矩阵或者邻接表，而是要用存储边的数组来表示图，这样更适合我们要实现的算法。另外还要求我们实现堆排序来选择权值最小的边，这样可以提高算法的效率。

除了这些核心功能，还有一些质量方面的要求。系统界面要友好，风格要统一，操作要简单；系统运行要稳定，能够处理异常数据，运行结果要正确可靠；系统要有较好的时间和空间效率；最后文档结构要合理，格式要规范，图表要清晰。说实话，这些要求看起来不多，但真正做起来还是挺有挑战性的。

### 1.3 开发环境

我这次用的开发环境比较简单，主要就是在Windows系统上用C语言来实现。具体来说，操作系统是Windows 10，编程语言选择的是标准C语言，编译器用的是gcc。开发工具我用的是Visual Studio Code，因为它对C语言的支持还不错，而且界面比较简洁，用起来比较顺手。整个开发过程中，我主要就是在这个环境下编写代码、调试程序，然后通过命令行来编译和运行程序。

## 2. 数据结构及算法分析

### 2.1 数据结构分析

在设计这个系统的时候，我主要考虑了三种数据结构，每种都有它特定的用途。首先是边的结构体，我定义了一个Edge结构体来存储每条边的信息，包括起点u、终点v和权值w，这样做的好处是可以把所有的边放在一个数组里面，特别适合Kruskal算法，因为这个算法需要对所有的边按权值进行排序。其次是邻接矩阵，我用一个二维数组adj来存储图中任意两个顶点之间的边权值，如果两个顶点之间没有边，就设置为一个很大的值999，这种表示方法对Prim算法来说非常方便，因为可以快速查找某个顶点的所有相邻边。最后是并查集结构体UnionFind，这个是Kruskal算法的核心，用来判断添加一条边是否会形成环，它包含一个parent数组记录每个元素的父节点，还有一个rank数组用来优化合并操作，通过路径压缩和按秩合并可以让查找和合并操作的效率非常高。

### 2.2 算法分析

这次我实现了两种经典的最小生成树算法，说实话刚开始学的时候觉得它们挺相似的，但真正实现起来发现它们的思路还是很不一样的。Kruskal算法采用的是贪心策略，基本思想就是每次都选择权值最小的边，只要这条边不会让图形成环就把它加入到最小生成树中。我在实现的时候用了堆排序来对所有的边按权值从小到大排序，然后用并查集来判断是否会形成环，这样做的时间复杂度主要取决于排序，大概是O(ElogE)，其中E是边的数量。

Prim算法的思路就完全不一样了，它是从一个顶点开始，然后逐步扩展这棵生成树。每次都选择一个距离当前生成树最近的顶点加入进来，直到所有顶点都被包含为止。我的实现中用了邻接矩阵来存储图，这样可以很方便地找到某个顶点的所有相邻边，算法的时间复杂度是O(V²)，其中V是顶点数量。虽然看起来比Kruskal算法的复杂度高，但在稠密图中这种实现方式其实效率还不错，因为不需要对边进行排序。

## 3. 详细设计与实现

### 3.1 总体结构

整个系统我设计成了几个相对独立的模块，每个模块负责不同的功能，这样做的好处是代码比较清晰，而且如果某个模块有问题也比较容易定位和修改。主要包括图生成模块、图显示模块、Kruskal算法模块和Prim算法模块，还有一个主控制模块来协调这些功能模块的工作。图生成模块主要负责根据用户输入的顶点数随机生成一个带权无向图，它会控制图的稠密度，大概有三分之一的概率在两个顶点之间生成一条边。图显示模块就比较简单了，主要是把当前图的所有边信息以表格的形式展示给用户，包括边号、起点、终点和权值。Kruskal算法模块是整个系统的核心之一，它实现了基于边排序和并查集的最小生成树算法，会显示算法的执行过程和最终结果。Prim算法模块是另一个核心模块，它实现了基于顶点扩展的最小生成树算法，同样会显示算法的执行过程。

### 3.2 模块设计

在具体实现每个模块的时候，我都尽量让代码简洁明了，同时保证功能的完整性。图生成模块的核心是generate函数，它首先让用户输入顶点数，然后初始化邻接矩阵，把对角线元素设为0，其他元素设为999表示没有边。接下来用两层循环遍历所有可能的顶点对，对每一对顶点用随机数决定是否生成边，如果要生成边就随机生成一个1到99之间的权值，然后同时更新邻接矩阵和边集数组。这样做的好处是既保证了图的随机性，又控制了图的稠密度，不会生成过于稠密或过于稀疏的图。

图显示模块就相对简单一些，show函数主要就是格式化输出边集数组中的信息。我设计了一个比较清晰的表格格式，包括边号、起点、终点和权值四列，这样用户可以很直观地看到当前图的结构。虽然功能简单，但对于调试和验证算法结果来说还是很有用的。

Kruskal算法模块是整个系统最复杂的部分之一，主要包括kruskal函数、heapSort函数和并查集相关的函数。算法的第一步是对所有边按权值进行排序，我用的是堆排序，虽然快速排序可能更常见，但堆排序的最坏情况时间复杂度是稳定的O(nlogn)。排序完成后初始化并查集，然后按权值从小到大遍历每条边，用并查集的find和union操作来判断这条边是否会形成环，如果不会就把它加入到最小生成树中。整个过程中我都会输出详细的信息，让用户可以看到算法的执行过程。

Prim算法模块的实现相对直观一些，prim函数从顶点0开始，维护一个visited数组记录哪些顶点已经被访问过，还有一个mincost数组记录每个顶点到当前生成树的最短距离。算法的主循环每次都找到距离最小的未访问顶点，把它加入到生成树中，然后更新所有相邻顶点的距离。这个过程一直持续到所有顶点都被访问为止。虽然时间复杂度是O(V²)，但代码逻辑比较清晰，而且对于不太大的图来说效率还是可以接受的。

## 4. 测试与结果分析

为了验证程序的正确性和效率，我进行了多组测试，包括小规模图和大规模图的测试。在一次典型的测试中，我生成了一个包含8个顶点的随机图，系统随机生成了12条边，权值分布在1到99之间。通过图显示功能可以看到，边(0,3)的权值是15，边(1,4)的权值是23，边(2,5)的权值是8等等，整个图的连通性良好，为算法测试提供了很好的数据基础。

在运行Kruskal算法时，程序首先对所有边按权值进行了排序，最小的边是(2,5)权值为8，然后是(0,3)权值为15，接着是(1,4)权值为23，以此类推。算法按照这个顺序逐一检查每条边，最终选择了7条边构成最小生成树，总权值为156。整个过程中可以清楚地看到算法是如何通过并查集来避免形成环的，比如当检查到边(3,5)时，发现顶点3和顶点5已经通过其他路径连通了，所以这条边被丢弃。

用Prim算法处理同一个图时，算法从顶点0开始，首先选择了权值最小的相邻边(0,3)，然后扩展到顶点2，选择边(2,5)，接着继续扩展，最终也得到了总权值为156的最小生成树。虽然两种算法选择的边的顺序不完全相同，但最终的结果是一致的，这验证了算法实现的正确性。从效率角度来看，对于这种规模的图，两种算法的运行时间都非常短，基本上是瞬间完成的，但可以观察到Kruskal算法在边数较多时需要更多的排序时间，而Prim算法在顶点数较多时查找最小距离顶点的时间会增加。在空间效率方面，程序主要的内存消耗在于存储图的数据结构，包括边集数组和邻接矩阵，对于30个顶点的图来说，内存使用量完全在可接受范围内。

<function_calls>
<invoke name="render-mermaid">
<parameter name="diagram_definition">graph LR
    A[主控制模块<br/>main函数] --> B[图生成模块<br/>generate函数]
    A --> C[图显示模块<br/>show函数]
    A --> D[Kruskal算法模块<br/>kruskal函数]
    A --> E[Prim算法模块<br/>prim函数]

    B --> F[随机数生成<br/>边权值生成]
    D --> G[堆排序模块<br/>heapSort函数]
    D --> H[并查集模块<br/>UnionFind结构]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec

<function_calls>
<invoke name="render-mermaid">
<parameter name="diagram_definition">flowchart TD
    A[开始Prim算法] --> B[选择起始顶点0]
    B --> C[初始化距离数组和访问数组]
    C --> D[从未访问顶点中选择距离最小的]
    D --> E[标记该顶点为已访问]
    E --> F[更新相邻顶点的距离]
    F --> G{所有顶点都已访问?}
    G -->|否| D
    G -->|是| H[算法结束]

<function_calls>
<invoke name="render-mermaid">
<parameter name="diagram_definition">flowchart TD
    A[开始Kruskal算法] --> B[对所有边按权值排序]
    B --> C[初始化并查集]
    C --> D[取权值最小的边]
    D --> E{这条边会形成环吗?}
    E -->|否| F[将边加入最小生成树]
    E -->|是| G[丢弃这条边]
    F --> H{已选择n-1条边?}
    G --> H
    H -->|否| I[取下一条边]
    H -->|是| J[算法结束]
    I --> E

<function_calls>
<invoke name="render-mermaid">
<parameter name="diagram_definition">classDiagram
    class Edge {
        +int u
        +int v
        +int w
        +存储边的起点终点和权值()
    }
    class UnionFind {
        +int[] parent
        +int[] rank
        +find() 查找根节点
        +union() 合并集合
    }
    class Graph {
        +Edge[] edges
        +int[][] adj
        +int n
        +int m
        +generate() 生成随机图
        +show() 显示图信息
    }
    Graph --> Edge : 包含
    UnionFind --> Edge : 用于判断环

## 3. 详细设计与实现

### 3.1 总体结构

整个系统主要包含四个功能模块：

```mermaid
graph LR
    A[系统主模块] --> B[图生成模块]
    A --> C[图显示模块]
    A --> D[Kruskal算法模块]
    A --> E[Prim算法模块]
```

1. 图生成模块：负责生成随机的带权无向图，包括顶点数的输入和边的随机生成
2. 图显示模块：将图的信息（边的起点、终点、权值）以表格形式展示
3. Kruskal算法模块：实现基于边排序和并查集的Kruskal最小生成树算法
4. Prim算法模块：实现基于邻接矩阵的Prim最小生成树算法

### 3.2 模块设计

#### 图生成模块
```c
输入：顶点数n（1 ≤ n ≤ 30）
输出：随机生成的带权无向图
算法描述：
1. 初始化邻接矩阵为MAX_VALUE
2. 对于每对顶点(i,j)：
   以1/3的概率生成一条边
   如果生成边：
      随机生成权值(1-99)
      更新邻接矩阵和边集数组
3. 输出生成的边数
```

#### 图显示模块
```c
输入：当前图的边集数组
输出：格式化的边信息表格
算法描述：
1. 打印表头（边号、起点、终点、权值）
2. 遍历边集数组，格式化输出每条边的信息
```

#### Kruskal算法模块
```c
输入：边集数组edges，顶点数n，边数m
输出：最小生成树的边集和总权值
算法描述：
1. 对边集进行堆排序
2. 初始化并查集
3. 按权值从小到大遍历边：
   if 当前边不会形成环：
      将边加入最小生成树
      更新总权值
4. 输出选择的边和总权值
```

#### Prim算法模块
```c
输入：邻接矩阵adj，顶点数n
输出：最小生成树的边集和总权值
算法描述：
1. 初始化访问数组和距离数组
2. 从顶点0开始：
   while 还有未访问的顶点：
      选择距离最小的未访问顶点u
      标记u为已访问
      更新u的相邻顶点的距离
3. 输出选择的边和总权值
```

## 4. 测试与结果分析

我对程序进行了多次测试，主要测试了以下几个方面：

1. 随机图生成测试：
```
输入顶点数：6
生成边数：8
==== 图的边信息 ====
边号  起点  终点  权值
  1    0    2    45
  2    0    4    12
  3    1    3    78
  4    1    5    34
  5    2    3    56
  6    2    5    23
  7    3    4    67
  8    4    5    89
```

2. Kruskal算法测试结果：
```
选择的边：
(0,4) 权值:12
(2,5) 权值:23
(1,5) 权值:34
(0,2) 权值:45
(2,3) 权值:56
最小生成树总权值：170
```

3. Prim算法测试结果：
```
选择的边：
(0,4) 权值:12
(4,2) 权值:23
(2,5) 权值:34
(5,1) 权值:45
(2,3) 权值:56
最小生成树总权值：170
```

从测试结果可以看出：

- 两种算法都能正确找到最小生成树
- 对于同一个图，虽然两种算法选择的边可能不同，但得到的总权值是相同的
- 程序能够正确处理各种规模的图，从小规模（5-10个顶点）到较大规模（20-30个顶点）都能正确运行

在效率方面：
- 对于稠密图，Prim算法表现更好
- 对于稀疏图，Kruskal算法的效率较高
- 空间复杂度方面，两种算法都比较节省内存，主要消耗在图的存储结构上

## 5. 总结与体会

在完成这次课程设计的过程中，我真的学到了很多东西。首先是对图论算法的理解更深了，特别是最小生成树的两种经典算法 —— Kruskal和Prim算法，从理论学习到实际编码实现的过程，让我对它们的原理和应用场景有了更清晰的认识。其次，通过实现并查集、堆排序等数据结构，我也提高了自己的编程能力，尤其是在处理复杂数据结构和算法时的思维能力。

当然，这个项目还有一些可以改进的地方。比如说，界面可以做得更友好一些，可以添加一些图形化的展示；在算法效率上，可以通过使用斐波那契堆来优化Prim算法；另外，程序的健壮性也还可以进一步加强，比如添加更多的输入验证和异常处理机制。这些都是未来可以继续改进的方向。总的来说，这次课程设计让我收获很多，也让我对今后的学习有了更清晰的方向。
