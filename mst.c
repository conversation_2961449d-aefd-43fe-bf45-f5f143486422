#include <stdio.h>
#include <stdlib.h>
#include <time.h>

#define MAX_V 30    // 最大顶点数
#define MAX_E 435   // 最大边数

// 边
typedef struct {
    int u, v;       // 边的两个顶点
    int w;          // 边的权值
} Edge;

// 并查集
typedef struct {
    int parent[MAX_V];
    int rank[MAX_V];
} UnionFind;

Edge edges[MAX_E];      // 边数组
int adj[MAX_V][MAX_V];  // 邻接矩阵
int n, m;               // 顶点数和边数

// 初始化并查集
void init(UnionFind *uf, int n) {
    int i;
    for (i = 0; i < n; i++) {
        uf->parent[i] = i;
        uf->rank[i] = 0;
    }
}

// 查找根节点
int find(UnionFind *uf, int x) {
    if (uf->parent[x] != x) {
        uf->parent[x] = find(uf, uf->parent[x]);
    }
    return uf->parent[x];
}

// 合并两个集合
int unionSet(UnionFind *uf, int x, int y) {
    int rootX = find(uf, x);
    int rootY = find(uf, y);
    
    if (rootX == rootY) return 0;  // 已经在同一集合
    
    if (uf->rank[rootX] < uf->rank[rootY]) {
        uf->parent[rootX] = rootY;
    } else if (uf->rank[rootX] > uf->rank[rootY]) {
        uf->parent[rootY] = rootX;
    } else {
        uf->parent[rootY] = rootX;
        uf->rank[rootX]++;
    }
    return 1;
}

// 堆调整(最大堆)
void heapify(Edge arr[], int n, int i) {
    int largest = i;
    int left = 2 * i + 1;
    int right = 2 * i + 2;
    Edge temp;

    if (left < n && arr[left].w > arr[largest].w)
        largest = left;

    if (right < n && arr[right].w > arr[largest].w)
        largest = right;

    if (largest != i) {
        temp = arr[i];
        arr[i] = arr[largest];
        arr[largest] = temp;
        heapify(arr, n, largest);
    }
}

// 堆排序
void heapSort(Edge arr[], int n) {
    int i;
    Edge temp;
    // 建堆
    for (i = n / 2 - 1; i >= 0; i--)
        heapify(arr, n, i);
    // 排序
    for (i = n - 1; i >= 0; i--) {
        temp = arr[0];
        arr[0] = arr[i];
        arr[i] = temp;
        heapify(arr, i, 0);
    }
}

// 生成随机图
void generate() {
    int i, j, w;
    printf("请输入顶点数(不超过30): ");
    scanf("%d", &n);
    // 初始化邻接矩阵
    for (i = 0; i < n; i++) {
        for (j = 0; j < n; j++) {
            adj[i][j] = (i == j) ? 0 : 999;  // 999表示无穷大
        }
    }
    m = 0;
    srand(time(NULL));
    // 随机生成边
    for (i = 0; i < n; i++) {
        for (j = i + 1; j < n; j++) {
            if (rand() % 3 == 0) {  // 约1/3的概率生成边
                w = rand() % 99 + 1;  // 权值1-99
                edges[m].u = i;
                edges[m].v = j;
                edges[m].w = w;
                adj[i][j] = adj[j][i] = w;
                m++;
            }
        }
    }
    printf("生成了%d条边的图\n", m);
}

// 显示图的边
void show() {
    int i;
    printf("\n当前图的所有边是:\n");
    printf("边号  起点  终点  权值\n");
    for (i = 0; i < m; i++) {
        printf("%3d   %3d   %3d   %3d\n", i+1, edges[i].u, edges[i].v, edges[i].w);
    }
}

// Kruskal算法
void kruskal() {
    UnionFind uf;
    Edge mst[MAX_V];
    int i, mstSize = 0, total = 0;
    if (m == 0) {
        printf("请先生成图!\n");
        return;
    }
    // 对边按权值排序
    heapSort(edges, m);
    // 初始化并查集
    init(&uf, n);
    printf("按权值排序后的边是:\n");
    for (i = 0; i < m; i++) {
        printf("边(%d,%d) 权值:%d\n", edges[i].u, edges[i].v, edges[i].w);
    }
    printf("\n选择的边是:\n");
    for (i = 0; i < m && mstSize < n - 1; i++) {
        if (unionSet(&uf, edges[i].u, edges[i].v)) {
            mst[mstSize] = edges[i];
            total += edges[i].w;
            printf("选择边(%d,%d) 权值:%d\n", edges[i].u, edges[i].v, edges[i].w);
            mstSize++;
        }
    }
    printf("\n最小生成树总权值是: %d\n", total);
}

// Prim算法
void prim() {
    int visited[MAX_V] = {0};
    int mincost[MAX_V];
    int parent[MAX_V];
    int i, j, u, v, minWeight, total = 0;
    if (n == 0) {
        printf("请你先生成图!\n");
        return;
    }
    // 初始化
    for (i = 0; i < n; i++) {
        mincost[i] = 999;
        parent[i] = -1;
    }
    // 从顶点0开始
    mincost[0] = 0;
    printf("选择的边:\n");
    for (i = 0; i < n; i++) {
        // 找最小权值的未访问顶点
        u = -1;
        for (j = 0; j < n; j++) {
            if (!visited[j] && (u == -1 || mincost[j] < mincost[u])) {
                u = j;
            }
        }
        visited[u] = 1;
        if (parent[u] != -1) {
            printf("选择边(%d,%d) 权值:%d\n", parent[u], u, mincost[u]);
            total += mincost[u];
        }
        // 更新相邻顶点的最小代价
        for (v = 0; v < n; v++) {
            if (!visited[v] && adj[u][v] < mincost[v]) {
                mincost[v] = adj[u][v];
                parent[v] = u;
            }
        }
    }
    printf("\n最小生成树总权值是: %d\n", total);
}

int main() {
    char choice;
    while (1) {
        printf("\n========================\n");
        printf("     ★   菜单  ★\n");
        printf("========================\n");
        printf("  a. 生成随机图\n");
        printf("  b. 显示图的边\n");
        printf("  c. Kruskal算法\n");
        printf("  d. Prim算法\n");
        printf("  e. 退出程序\n");
        printf("========================\n");
        printf("请选择操作: ");
        scanf(" %c", &choice);
        switch (choice) {
            case 'a':
            case 'A':
                generate();
                break;
            case 'b':
            case 'B':
                show();
                break;
            case 'c':
            case 'C':
                kruskal();
                break;
            case 'd':
            case 'D':
                prim();
                break;
            case 'e':
            case 'E':
                printf("下次再见!\n");
                return 0;
            default:
                printf("你的输入有错误，请重新选择一下吧!\n");
        }
    }
    return 0;
}
